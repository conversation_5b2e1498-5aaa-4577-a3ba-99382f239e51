"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Building, 
  Plus,
  Filter,
  Search,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Shield,
  Scan,
  Edit,
  Archive
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

import { sampleITAssets } from "@/lib/assets-data"
import { ITAsset, CriticalityLevel, SecurityStatus } from "@/types/assets"
import { ITAssetDetailView } from "@/components/assets/it-asset-detail-view"

const criticalityConfig = {
  critical: { label: "Critical", variant: "destructive" as const, color: "text-red-600 dark:text-red-400" },
  high: { label: "High", variant: "destructive" as const, color: "text-orange-600 dark:text-orange-400" },
  medium: { label: "Medium", variant: "secondary" as const, color: "text-yellow-600 dark:text-yellow-400" },
  low: { label: "Low", variant: "outline" as const, color: "text-green-600 dark:text-green-400" }
}

const securityStatusConfig = {
  compliant: { 
    label: "Compliant", 
    variant: "default" as const, 
    icon: CheckCircle, 
    color: "text-green-600 dark:text-green-400" 
  },
  "non-compliant": { 
    label: "Non-Compliant", 
    variant: "destructive" as const, 
    icon: XCircle, 
    color: "text-red-600 dark:text-red-400" 
  },
  unknown: { 
    label: "Unknown", 
    variant: "secondary" as const, 
    icon: AlertTriangle, 
    color: "text-gray-600 dark:text-gray-400" 
  },
  pending: { 
    label: "Pending", 
    variant: "outline" as const, 
    icon: Clock, 
    color: "text-blue-600 dark:text-blue-400" 
  }
}

const assetTypeIcons = {
  server: Building,
  workstation: Building,
  laptop: Building,
  "mobile-device": Building,
  "network-device": Building,
  "storage-device": Building,
  printer: Building,
  "virtual-machine": Building,
  container: Building
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return "Just now"
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

function getRiskColor(score: number): string {
  if (score >= 70) return "text-red-600 dark:text-red-400"
  if (score >= 50) return "text-orange-600 dark:text-orange-400"
  if (score >= 30) return "text-yellow-600 dark:text-yellow-400"
  return "text-green-600 dark:text-green-400"
}

export default function ITAssetsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [assets] = useState<ITAsset[]>(sampleITAssets)
  const [selectedAsset, setSelectedAsset] = useState<ITAsset | null>(null)

  const filteredAssets = assets.filter(asset =>
    asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.assetType.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.owner.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">IT Assets</h1>
            <p className="text-muted-foreground">
              Servers, workstations, network infrastructure with hardware specifications and software inventory
            </p>
          </div>
          <Button asChild>
            <Link href="/assets/register?category=it">
              <Plus className="mr-2 h-4 w-4" />
              Add IT Asset
            </Link>
          </Button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto space-y-6">

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total IT Assets</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assets.length}</div>
            <p className="text-xs text-muted-foreground">
              Active infrastructure
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Assets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.criticalityLevel === "critical").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliant</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {assets.filter(a => a.securityStatus === "compliant").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Meeting security standards
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Risk Score</CardTitle>
            <Shield className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(assets.reduce((sum, a) => sum + a.riskScore, 0) / assets.length)}
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 100
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search IT assets..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline" size="sm">
          <Scan className="mr-2 h-4 w-4" />
          Scan Network
        </Button>
      </div>

      {/* Assets Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Criticality</TableHead>
              <TableHead>Security Status</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Risk Score</TableHead>
              <TableHead>Compliance</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAssets.map((asset) => {
              const criticalityInfo = criticalityConfig[asset.criticalityLevel]
              const statusInfo = securityStatusConfig[asset.securityStatus]
              const StatusIcon = statusInfo.icon
              const AssetIcon = assetTypeIcons[asset.assetType] || Building
              
              return (
                <TableRow key={asset.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <AssetIcon className="h-4 w-4 text-muted-foreground" />
                      <div className="space-y-1">
                        <Link
                          href={`/assets/it/${asset.id}`}
                          className="font-medium text-left hover:text-primary cursor-pointer"
                        >
                          {asset.name}
                        </Link>
                        {asset.description && (
                          <div className="text-sm text-muted-foreground line-clamp-1">
                            {asset.description}
                          </div>
                        )}
                        {asset.network?.ipAddress && (
                          <div className="text-xs text-muted-foreground">
                            {asset.network.ipAddress}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {asset.assetType.replace("-", " ")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={criticalityInfo.variant}>
                      {criticalityInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <StatusIcon className={`h-4 w-4 ${statusInfo.color}`} />
                      <Badge variant={statusInfo.variant}>
                        {statusInfo.label}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {formatRelativeTime(asset.lastSeen)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {asset.discoveryMethod}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className={`text-sm font-medium ${getRiskColor(asset.riskScore)}`}>
                        {asset.riskScore}
                      </div>
                      <Progress value={asset.riskScore} className="h-1" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {Object.entries(asset.complianceStatus).map(([framework, status]) => (
                        <div key={framework} className="flex items-center gap-1">
                          <Badge 
                            variant={status.status === "compliant" ? "default" : "destructive"}
                            className="text-xs"
                          >
                            {framework}
                          </Badge>
                          {status.gaps > 0 && (
                            <span className="text-xs text-red-600">
                              {status.gaps} gaps
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{asset.owner}</div>
                      <div className="text-xs text-muted-foreground">
                        {asset.responsibleParty}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/assets/it/${asset.id}`}>
                            <Building className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Scan className="mr-2 h-4 w-4" />
                          Scan
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Shield className="mr-2 h-4 w-4" />
                          Remediate
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Archive className="mr-2 h-4 w-4" />
                          Archive
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </Card>
      </div>

      {/* Modal for asset details */}
      {selectedAsset && (
        <ITAssetDetailView
          asset={selectedAsset}
          onClose={() => setSelectedAsset(null)}
          onEdit={(asset) => {
            console.log("Edit asset:", asset)
            setSelectedAsset(null)
          }}
          onScan={(asset) => {
            console.log("Scan asset:", asset)
          }}
          onArchive={(asset) => {
            console.log("Archive asset:", asset)
            setSelectedAsset(null)
          }}
        />
      )}
    </div>
  )
}
