"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Package, 
  Plus,
  Search,
  TrendingUp,
  Shield,
  AlertTriangle,
  Activity,
  Scan,
  Network,
  Users,
  Building,
  Workflow
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

import { assetStats } from "@/lib/assets-data"

export default function AssetsPage() {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Assets</h1>
            <p className="text-muted-foreground">
              Comprehensive asset management with blockchain-secured CMDB, automated discovery, and risk assessment
            </p>
          </div>
          <Button asChild>
            <Link href="/assets/register">
              <Plus className="mr-2 h-4 w-4" />
              Add Asset
            </Link>
          </Button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto space-y-6">{/* Content will be added here */}

      {/* Asset Portfolio Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assetStats.totalAssets}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Assets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assetStats.criticalAssets}</div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Risk Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assetStats.averageRiskScore}</div>
            <p className="text-xs text-muted-foreground">
              Out of 100
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Discovery Health</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assetStats.discoveryHealth}%</div>
            <p className="text-xs text-muted-foreground">
              Discovery systems operational
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Asset Category Distribution */}
      <div className="grid gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Asset Category Distribution</CardTitle>
            <CardDescription>
              Breakdown of assets by category with compliance status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Building className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">IT Assets</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.it}</span>
                  <Progress value={(assetStats.assetsByCategory.it / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Workflow className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium">OT Assets</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.ot}</span>
                  <Progress value={(assetStats.assetsByCategory.ot / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Network className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">IoT Devices</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.iot}</span>
                  <Progress value={(assetStats.assetsByCategory.iot / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Users className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">Identities</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.identity}</span>
                  <Progress value={(assetStats.assetsByCategory.identity / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Package className="h-4 w-4 text-indigo-600" />
                  <span className="text-sm font-medium">Applications</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.application}</span>
                  <Progress value={(assetStats.assetsByCategory.application / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Building className="h-4 w-4 text-pink-600" />
                  <span className="text-sm font-medium">Vendors</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.vendor}</span>
                  <Progress value={(assetStats.assetsByCategory.vendor / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 flex-1">
                  <Workflow className="h-4 w-4 text-teal-600" />
                  <span className="text-sm font-medium">Processes</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{assetStats.assetsByCategory.process}</span>
                  <Progress value={(assetStats.assetsByCategory.process / assetStats.totalAssets) * 100} className="w-20 h-2" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Risk Exposure Summary</CardTitle>
            <CardDescription>
              High-risk assets and remediation progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">High Risk</span>
                </div>
                <span className="text-sm font-medium">{assetStats.highRiskAssets}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">Non-Compliant</span>
                </div>
                <span className="text-sm font-medium">{assetStats.nonCompliantAssets}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">Needs Attention</span>
                </div>
                <span className="text-sm font-medium">8</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Compliant</span>
                </div>
                <span className="text-sm font-medium">{assetStats.totalAssets - assetStats.nonCompliantAssets}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discovery Activity Monitor */}
      <Card>
        <CardHeader>
          <CardTitle>Discovery Activity Monitor</CardTitle>
          <CardDescription>
            Real-time asset discovery status and configuration change alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Network Discovery</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                Last scan: {assetStats.lastDiscovery.toLocaleDateString()}
              </div>
              <Progress value={100} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Agent-Based Discovery</span>
                <Badge variant="default">Running</Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                15 agents reporting
              </div>
              <Progress value={85} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Cloud API Integration</span>
                <Badge variant="outline">Scheduled</Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                Next sync in 2 hours
              </div>
              <Progress value={0} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="flex items-center space-x-4 p-6">
            <Scan className="h-8 w-8 text-blue-600" />
            <div>
              <div className="font-medium">Run Discovery</div>
              <div className="text-sm text-muted-foreground">Start network scan</div>
            </div>
          </CardContent>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="flex items-center space-x-4 p-6">
            <Plus className="h-8 w-8 text-green-600" />
            <div>
              <div className="font-medium">Bulk Import</div>
              <div className="text-sm text-muted-foreground">Import CSV/Excel</div>
            </div>
          </CardContent>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="flex items-center space-x-4 p-6">
            <Network className="h-8 w-8 text-purple-600" />
            <div>
              <div className="font-medium">View Topology</div>
              <div className="text-sm text-muted-foreground">Asset relationships</div>
            </div>
          </CardContent>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="flex items-center space-x-4 p-6">
            <Shield className="h-8 w-8 text-orange-600" />
            <div>
              <div className="font-medium">Risk Assessment</div>
              <div className="text-sm text-muted-foreground">Analyze risks</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Navigation */}
      <Card>
        <CardHeader>
          <CardTitle>Asset Categories</CardTitle>
          <CardDescription>
            Browse assets by category or search across all asset types
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search assets across all categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/it">
                  <Building className="mr-3 h-5 w-5 text-blue-600" />
                  <div className="text-left">
                    <div className="font-medium">IT Assets</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.it} assets</div>
                  </div>
                </Link>
              </Button>
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/ot">
                  <Workflow className="mr-3 h-5 w-5 text-orange-600" />
                  <div className="text-left">
                    <div className="font-medium">OT Assets</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.ot} assets</div>
                  </div>
                </Link>
              </Button>
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/iot">
                  <Network className="mr-3 h-5 w-5 text-green-600" />
                  <div className="text-left">
                    <div className="font-medium">IoT Devices</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.iot} devices</div>
                  </div>
                </Link>
              </Button>
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/identities">
                  <Users className="mr-3 h-5 w-5 text-purple-600" />
                  <div className="text-left">
                    <div className="font-medium">Identities</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.identity} identities</div>
                  </div>
                </Link>
              </Button>
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/applications">
                  <Package className="mr-3 h-5 w-5 text-indigo-600" />
                  <div className="text-left">
                    <div className="font-medium">Applications</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.application} applications</div>
                  </div>
                </Link>
              </Button>
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/vendors">
                  <Building className="mr-3 h-5 w-5 text-pink-600" />
                  <div className="text-left">
                    <div className="font-medium">Vendors</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.vendor} vendors</div>
                  </div>
                </Link>
              </Button>
              <Button variant="outline" asChild className="justify-start h-auto p-4">
                <Link href="/assets/processes">
                  <Workflow className="mr-3 h-5 w-5 text-teal-600" />
                  <div className="text-left">
                    <div className="font-medium">Processes</div>
                    <div className="text-xs text-muted-foreground">{assetStats.assetsByCategory.process} processes</div>
                  </div>
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
