"use client"

import React from "react"
import { <PERSON><PERSON>, Navigation, Eye } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function AssetsStickyTestPage() {
  // Generate content to test scrolling
  const sections = Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    title: `Test Section ${i + 1}`,
    content: `This is test content for section ${i + 1}. This section contains enough content to demonstrate the sticky sub-navigation behavior when scrolling through the page. The sub-navigation should remain visible at the top of the content area while staying below the system top bar.`
  }))

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sticky Sub-Navigation Test</h1>
            <p className="text-muted-foreground">
              Test page to verify sticky sub-navigation behavior with scrollable content
            </p>
          </div>
          <Badge variant="secondary" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Scroll Test
          </Badge>
        </div>
      </div>

      {/* Instructions */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            Sticky Navigation Test Instructions
          </CardTitle>
          <CardDescription>
            How to test the sticky sub-navigation behavior
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Expected Behavior</div>
              <ul className="text-sm space-y-1 list-disc list-inside">
                <li>Sub-navigation bar should remain visible when scrolling</li>
                <li>Sub-navigation should stay below the system top bar</li>
                <li>Sub-navigation should not overlap or cover the system top bar</li>
                <li>All sub-navigation tabs should remain functional while sticky</li>
              </ul>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Test Steps</div>
              <ol className="text-sm space-y-1 list-decimal list-inside">
                <li>Scroll down through the content sections below</li>
                <li>Observe that the sub-navigation remains at the top</li>
                <li>Click different sub-navigation tabs while scrolled</li>
                <li>Verify proper z-index layering with system top bar</li>
              </ol>
            </div>
          </div>
          
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <Scroll className="h-4 w-4" />
              <strong>Scroll down to test the sticky sub-navigation behavior</strong>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Scrollable Content Sections */}
      <div className="space-y-6">
        {sections.map((section) => (
          <Card key={section.id}>
            <CardHeader>
              <CardTitle>{section.title}</CardTitle>
              <CardDescription>
                Section {section.id} of {sections.length} - Testing scrollable content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {section.content}
              </p>
              
              {/* Add extra content for some sections */}
              {section.id % 3 === 0 && (
                <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Additional Content Block</h4>
                  <p className="text-sm text-muted-foreground">
                    This is additional content to make the page longer and provide more scrolling area. 
                    The sticky sub-navigation should remain visible and functional throughout the entire 
                    scrolling experience. This helps demonstrate that the navigation stays accessible 
                    regardless of how far down the user scrolls.
                  </p>
                </div>
              )}
              
              {/* Add numbered content for easy reference */}
              <div className="mt-4 grid gap-2 md:grid-cols-3">
                {Array.from({ length: 6 }, (_, i) => (
                  <div key={i} className="p-3 border rounded text-center">
                    <div className="text-lg font-bold text-primary">{section.id}.{i + 1}</div>
                    <div className="text-xs text-muted-foreground">Content Block</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bottom Spacer */}
      <div className="h-32 flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <div className="text-lg font-medium">End of Test Content</div>
          <div className="text-sm">Sub-navigation should still be visible and sticky at the top</div>
        </div>
      </div>
    </div>
  )
}
