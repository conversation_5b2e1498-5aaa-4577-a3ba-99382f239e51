"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus } from "lucide-react"

import { <PERSON>ton } from "@/components/ui/button"
import { StickySearch } from "@/components/sticky-search"
import { ControlTable } from "@/components/controls/control-table"
import { sampleControls } from "@/lib/controls-data"

export default function AllControlsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Implemented", value: "implemented" },
    { label: "In Progress", value: "in-progress" },
    { label: "Not Started", value: "not-started" },
    { label: "Under Review", value: "under-review" },
    { label: "Effective", value: "effective" },
    { label: "Partially Effective", value: "partial" },
    { label: "Ineffective", value: "ineffective" },
    { label: "Not Tested", value: "not-tested" },
    { label: "Preventive", value: "preventive" },
    { label: "Detective", value: "detective" },
    { label: "Corrective", value: "corrective" },
    { label: "High Risk", value: "high" },
    { label: "Medium Risk", value: "medium" },
    { label: "Low Risk", value: "low" },
  ]

  const sortOptions = [
    { label: "Name (A-Z)", value: "name-asc" },
    { label: "Name (Z-A)", value: "name-desc" },
    { label: "Updated (Newest)", value: "updated-desc" },
    { label: "Updated (Oldest)", value: "updated-asc" },
    { label: "Created (Newest)", value: "created-desc" },
    { label: "Created (Oldest)", value: "created-asc" },
    { label: "Effectiveness", value: "effectiveness" },
    { label: "Risk Level", value: "risk" },
    { label: "Control Type", value: "type" },
    { label: "Status", value: "status" },
  ]

  // Filter controls based on search and filters
  const filteredControls = sampleControls.filter(control => {
    const matchesSearch = (control.name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (control.description || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (control.category || '').toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesFilters = activeFilters.length === 0 || 
      activeFilters.some(filter => 
        control.status === filter || 
        control.effectiveness === filter ||
        control.controlType === filter ||
        control.riskLevel === filter
      )
    
    return matchesSearch && matchesFilters
  }).sort((a, b) => {
    switch (currentSort) {
      case "name-asc": return a.name.localeCompare(b.name)
      case "name-desc": return b.name.localeCompare(a.name)
      case "updated-desc": return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
      case "updated-asc": return new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime()
      case "created-desc": return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case "created-asc": return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      case "effectiveness": return (b.effectivenessScore || 0) - (a.effectivenessScore || 0)
      case "risk": return a.riskLevel.localeCompare(b.riskLevel)
      case "type": return a.controlType.localeCompare(b.controlType)
      case "status": return a.status.localeCompare(b.status)
      default: return 0
    }
  })

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Search */}
      <StickySearch
        placeholder="Search controls by name, description, or category..."
        onSearch={setSearchQuery}
        onFilterChange={setActiveFilters}
        onSortChange={setCurrentSort}
        filterOptions={filterOptions}
        sortOptions={sortOptions}
        actionButton={
          <Button asChild>
            <Link href="/controls/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Control
            </Link>
          </Button>
        }
      />

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <ControlTable controls={filteredControls} />
        
        {/* Results Summary */}
        <div className="text-sm text-muted-foreground text-center mt-6">
          Showing {filteredControls.length} of {sampleControls.length} controls
        </div>
      </div>
    </div>
  )
}
