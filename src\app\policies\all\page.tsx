"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus } from "lucide-react"

import { <PERSON>ton } from "@/components/ui/button"
import { StickySearch } from "@/components/sticky-search"
import { PolicyTable } from "@/components/policies/policy-table"
import { samplePolicies } from "@/lib/policies-data"

export default function AllPoliciesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Published", value: "published" },
    { label: "Draft", value: "draft" },
    { label: "Under Review", value: "review" },
    { label: "Archived", value: "archived" },
    { label: "Security", value: "security" },
    { label: "Privacy", value: "privacy" },
    { label: "Operational", value: "operational" },
    { label: "HR", value: "hr" },
    { label: "High Priority", value: "high" },
    { label: "Medium Priority", value: "medium" },
    { label: "Low Priority", value: "low" },
  ]

  const sortOptions = [
    { label: "Name (A-Z)", value: "name-asc" },
    { label: "Name (Z-A)", value: "name-desc" },
    { label: "Updated (Newest)", value: "updated-desc" },
    { label: "Updated (Oldest)", value: "updated-asc" },
    { label: "Created (Newest)", value: "created-desc" },
    { label: "Created (Oldest)", value: "created-asc" },
    { label: "Priority", value: "priority" },
    { label: "Status", value: "status" },
    { label: "Category", value: "category" },
  ]

  // Filter policies based on search and filters
  const filteredPolicies = samplePolicies.filter(policy => {
    const matchesSearch = (policy.name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (policy.description || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
      (policy.category || '').toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesFilters = activeFilters.length === 0 || 
      activeFilters.some(filter => 
        policy.status === filter || 
        policy.category.toLowerCase() === filter ||
        policy.priority === filter
      )
    
    return matchesSearch && matchesFilters
  }).sort((a, b) => {
    switch (currentSort) {
      case "name-asc": return a.name.localeCompare(b.name)
      case "name-desc": return b.name.localeCompare(a.name)
      case "updated-desc": return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
      case "updated-asc": return new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime()
      case "created-desc": return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case "created-asc": return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      case "priority": return a.priority.localeCompare(b.priority)
      case "status": return a.status.localeCompare(b.status)
      case "category": return a.category.localeCompare(b.category)
      default: return 0
    }
  })

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Search */}
      <StickySearch
        placeholder="Search policies by name, description, or category..."
        onSearch={setSearchQuery}
        onFilterChange={setActiveFilters}
        onSortChange={setCurrentSort}
        filterOptions={filterOptions}
        sortOptions={sortOptions}
        actionButton={
          <Button asChild>
            <Link href="/policies/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Policy
            </Link>
          </Button>
        }
      />

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <PolicyTable policies={filteredPolicies} />
        
        {/* Results Summary */}
        <div className="text-sm text-muted-foreground text-center mt-6">
          Showing {filteredPolicies.length} of {samplePolicies.length} policies
        </div>
      </div>
    </div>
  )
}
