"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, Clock, CheckCircle, XCircle, AlertTriangle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StickySearch } from "@/components/sticky-search"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock exceptions data
const mockExceptions = [
  {
    id: "E001",
    policyName: "Access Control Policy",
    requestedBy: "<PERSON> Doe",
    department: "Audit",
    reason: "Temporary access for annual audit",
    status: "Approved",
    requestedAt: "2024-01-15T09:00:00Z",
    expiresAt: "2024-02-15T23:59:59Z",
    approvedBy: "Security Team",
    riskLevel: "Low",
  },
  {
    id: "E002",
    policyName: "Data Retention Policy",
    requestedBy: "Legal Team",
    department: "Legal",
    reason: "Litigation hold requirement",
    status: "Pending",
    requestedAt: "2024-01-18T14:30:00Z",
    expiresAt: "2024-06-18T23:59:59Z",
    approvedBy: null,
    riskLevel: "Medium",
  },
  {
    id: "E003",
    policyName: "Password Policy",
    requestedBy: "System Admin",
    department: "IT",
    reason: "Service account configuration",
    status: "Expired",
    requestedAt: "2023-12-01T10:00:00Z",
    expiresAt: "2024-01-01T23:59:59Z",
    approvedBy: "IT Manager",
    riskLevel: "High",
  },
]

export default function PolicyExceptionsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Approved", value: "approved" },
    { label: "Pending", value: "pending" },
    { label: "Rejected", value: "rejected" },
    { label: "Expired", value: "expired" },
    { label: "Low Risk", value: "low" },
    { label: "Medium Risk", value: "medium" },
    { label: "High Risk", value: "high" },
    { label: "Critical Risk", value: "critical" },
  ]

  const sortOptions = [
    { label: "Requested (Newest)", value: "requested-desc" },
    { label: "Requested (Oldest)", value: "requested-asc" },
    { label: "Expires (Soonest)", value: "expires-asc" },
    { label: "Expires (Latest)", value: "expires-desc" },
    { label: "Risk Level", value: "risk" },
    { label: "Status", value: "status" },
    { label: "Policy Name (A-Z)", value: "policy-asc" },
    { label: "Department", value: "department" },
  ]

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
      case "pending": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
      case "rejected": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
      case "expired": return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case "critical": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
      case "high": return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "pending": return <Clock className="h-4 w-4 text-yellow-600" />
      case "rejected": return <XCircle className="h-4 w-4 text-red-600" />
      case "expired": return <AlertTriangle className="h-4 w-4 text-gray-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const isExpiringSoon = (expiresAt: string) => {
    const expiry = new Date(expiresAt)
    const now = new Date()
    const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0
  }

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Search */}
      <StickySearch
        placeholder="Search exceptions by policy, requester, or reason..."
        onSearch={setSearchQuery}
        onFilterChange={setActiveFilters}
        onSortChange={setCurrentSort}
        filterOptions={filterOptions}
        sortOptions={sortOptions}
        actionButton={
          <Button asChild>
            <Link href="/policies/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Policy
            </Link>
          </Button>
        }
      />

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Exceptions</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">15</div>
              <p className="text-xs text-muted-foreground">
                +2 from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                Currently approved
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                Awaiting approval
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2</div>
              <p className="text-xs text-muted-foreground">
                Within 7 days
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Exceptions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Policy Exceptions</CardTitle>
            <CardDescription>
              Approved policy exceptions and their expiration tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Exception ID</TableHead>
                  <TableHead>Policy</TableHead>
                  <TableHead>Requester</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Risk Level</TableHead>
                  <TableHead>Expires</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockExceptions.map((exception) => (
                  <TableRow key={exception.id}>
                    <TableCell className="font-medium">{exception.id}</TableCell>
                    <TableCell>
                      <div className="font-medium">{exception.policyName}</div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{exception.requestedBy}</div>
                        <div className="text-sm text-muted-foreground">{exception.department}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={exception.reason}>
                        {exception.reason}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(exception.status)}
                        <Badge className={getStatusColor(exception.status)}>
                          {exception.status}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRiskColor(exception.riskLevel)}>
                        {exception.riskLevel}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className={isExpiringSoon(exception.expiresAt) ? "text-orange-600 font-medium" : ""}>
                        {new Date(exception.expiresAt).toLocaleDateString()}
                        {isExpiringSoon(exception.expiresAt) && (
                          <div className="text-xs text-orange-600">Expiring soon</div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
