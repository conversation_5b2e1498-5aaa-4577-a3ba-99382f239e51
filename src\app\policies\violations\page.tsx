"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock, CheckCircle, XCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StickySearch } from "@/components/sticky-search"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Mock violations data
const mockViolations = [
  {
    id: "V001",
    policyName: "Data Retention Policy",
    violationType: "Automated Detection",
    department: "Marketing",
    severity: "High",
    status: "Open",
    detectedAt: "2024-01-20T10:30:00Z",
    description: "Data retention period exceeded for customer records",
    assignedTo: "<PERSON>",
  },
  {
    id: "V002",
    policyName: "Password Policy",
    violationType: "Manual Report",
    department: "IT",
    severity: "Medium",
    status: "In Progress",
    detectedAt: "2024-01-19T14:15:00Z",
    description: "Weak passwords detected in system audit",
    assignedTo: "<PERSON>",
  },
  {
    id: "V003",
    policyName: "Access Control Policy",
    violationType: "System Alert",
    department: "Finance",
    severity: "Critical",
    status: "Resolved",
    detectedAt: "2024-01-18T09:45:00Z",
    description: "Unauthorized access attempt to financial systems",
    assignedTo: "Mike Davis",
  },
]

export default function PolicyViolationsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")

  // Filter and sort options
  const filterOptions = [
    { label: "Open", value: "open" },
    { label: "In Progress", value: "in-progress" },
    { label: "Resolved", value: "resolved" },
    { label: "Closed", value: "closed" },
    { label: "Critical", value: "critical" },
    { label: "High", value: "high" },
    { label: "Medium", value: "medium" },
    { label: "Low", value: "low" },
    { label: "Automated Detection", value: "automated" },
    { label: "Manual Report", value: "manual" },
    { label: "System Alert", value: "system" },
  ]

  const sortOptions = [
    { label: "Detected (Newest)", value: "detected-desc" },
    { label: "Detected (Oldest)", value: "detected-asc" },
    { label: "Severity (High-Low)", value: "severity-desc" },
    { label: "Severity (Low-High)", value: "severity-asc" },
    { label: "Policy Name (A-Z)", value: "policy-asc" },
    { label: "Policy Name (Z-A)", value: "policy-desc" },
    { label: "Status", value: "status" },
    { label: "Department", value: "department" },
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case "critical": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
      case "high": return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "open": return <AlertTriangle className="h-4 w-4 text-red-600" />
      case "in progress": return <Clock className="h-4 w-4 text-yellow-600" />
      case "resolved": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "closed": return <XCircle className="h-4 w-4 text-gray-600" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Search */}
      <StickySearch
        placeholder="Search violations by policy, department, or description..."
        onSearch={setSearchQuery}
        onFilterChange={setActiveFilters}
        onSortChange={setCurrentSort}
        filterOptions={filterOptions}
        sortOptions={sortOptions}
        actionButton={
          <Button asChild>
            <Link href="/policies/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Policy
            </Link>
          </Button>
        }
      />

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Violations</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">23</div>
              <p className="text-xs text-muted-foreground">
                +3 from last week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Open</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">7</div>
              <p className="text-xs text-muted-foreground">
                Being addressed
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resolved</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Violations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Policy Violations</CardTitle>
            <CardDescription>
              Active policy violations requiring attention and remediation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Violation ID</TableHead>
                  <TableHead>Policy</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Detected</TableHead>
                  <TableHead>Assigned To</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockViolations.map((violation) => (
                  <TableRow key={violation.id}>
                    <TableCell className="font-medium">{violation.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{violation.policyName}</div>
                        <div className="text-sm text-muted-foreground">{violation.description}</div>
                      </div>
                    </TableCell>
                    <TableCell>{violation.department}</TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(violation.severity)}>
                        {violation.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(violation.status)}
                        <span>{violation.status}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(violation.detectedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>{violation.assignedTo}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
