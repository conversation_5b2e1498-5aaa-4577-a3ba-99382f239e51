"use client"

import { usePathname } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { SystemTopBar } from "@/components/system-top-bar"
import { SubNavigation } from "@/components/top-navigation"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()

  // Pages that should not have the sidebar layout
  const excludedPaths = ["/login"]

  // Check if we should hide sub-navigation (for portal, artifacts, policies, and controls routes)
  // Note: Activity, Workflows, Overview, Monitor, Assessments, and Remediation should show sub-navigation like Assets
  const shouldHideSubNavigation = pathname.startsWith("/portals") || pathname.startsWith("/artifacts") || pathname.startsWith("/policies") || pathname.startsWith("/controls")

  // Check if this is a portal configurator page that needs full layout control
  const isPortalConfigurator = pathname.includes("/portals/configure/")

  // Check if this is a page that needs full height layout for sticky headers and proper overflow handling
  // Note: All modules with sub-navigation should use this layout pattern
  const isPoliciesPage = pathname.startsWith("/policies")
  const isControlsPage = pathname.startsWith("/controls")
  const isWorkflowsPage = pathname.startsWith("/workflows")
  const isRemediationPage = pathname.startsWith("/remediation")
  const isAssetsPage = pathname.startsWith("/assets")
  const isActivityPage = pathname.startsWith("/activity")
  const isOverviewPage = pathname.startsWith("/overview")
  const isMonitorPage = pathname.startsWith("/monitor")
  const isAssessmentsPage = pathname.startsWith("/assessments")

  const shouldExcludeSidebar = excludedPaths.includes(pathname)

  if (shouldExcludeSidebar) {
    return <>{children}</>
  }

  return (
    <div className="pt-12"> {/* Add top padding for fixed system bar */}
      <SidebarProvider>
        {/* System Top Bar - Fixed at top */}
        <SystemTopBar />

        {/* Main Layout with Sidebar */}
        <AppSidebar />
        <SidebarInset>
          {/* Sub-Navigation Bar - only show if not portal routes */}
          {!shouldHideSubNavigation && (
            <header className="flex h-12 shrink-0 items-center border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
              <SubNavigation />
            </header>
          )}

          {/* Main Content - different layout for special pages */}
          {isPortalConfigurator ? (
            <div className="flex flex-1 flex-col overflow-hidden">
              {children}
            </div>
          ) : isPoliciesPage || isControlsPage || isWorkflowsPage || isRemediationPage || isAssetsPage || isActivityPage || isOverviewPage || isMonitorPage || isAssessmentsPage ? (
            <div className="flex flex-1 flex-col p-4 h-full overflow-hidden">
              {children}
            </div>
          ) : (
            <div className="flex flex-1 flex-col gap-4 p-4">
              {children}
            </div>
          )}
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}
