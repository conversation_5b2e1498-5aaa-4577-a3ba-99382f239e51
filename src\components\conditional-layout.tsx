"use client"

import { usePathname } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { SystemTopBar } from "@/components/system-top-bar"
import { SubNavigation } from "@/components/top-navigation"
import { LayoutStickySearch } from "@/components/layout-sticky-search"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()

  // Pages that should not have the sidebar layout
  const excludedPaths = ["/login"]

  // Check if we should hide sub-navigation (for artifacts, reports, frameworks, and portals routes)
  // Note: Activity, Workflows, Overview, Monitor, Assessments, Remediation, Policies, and Controls show sub-navigation like Assets
  // Reports, Frameworks, Artifacts, and Portals use sticky search instead of sub-navigation
  const shouldHideSubNavigation = pathname.startsWith("/artifacts") || pathname.startsWith("/reports") || pathname.startsWith("/frameworks") || pathname.startsWith("/portals")

  // Check if we should show sticky search (for modules that use it instead of sub-navigation)
  const shouldShowStickySearch = pathname.startsWith("/artifacts") || pathname.startsWith("/reports") || pathname.startsWith("/frameworks") || pathname.startsWith("/portals")

  // Check if this is a portal configurator page that needs full layout control
  const isPortalConfigurator = pathname.includes("/portals/configure/")

  // Check if this is a page that needs full height layout for sticky headers and proper overflow handling
  // Note: All modules with sub-navigation should use this layout pattern
  // Reports, Frameworks, Artifacts, and Portals use standard layout with their own sticky search implementation
  const isPoliciesPage = pathname.startsWith("/policies")
  const isControlsPage = pathname.startsWith("/controls")
  const isWorkflowsPage = pathname.startsWith("/workflows")
  const isRemediationPage = pathname.startsWith("/remediation")
  const isAssetsPage = pathname.startsWith("/assets")
  const isActivityPage = pathname.startsWith("/activity")
  const isOverviewPage = pathname.startsWith("/overview")
  const isMonitorPage = pathname.startsWith("/monitor")
  const isAssessmentsPage = pathname.startsWith("/assessments")
  const isReportsPage = pathname.startsWith("/reports")
  const isFrameworksPage = pathname.startsWith("/frameworks")
  const isArtifactsPage = pathname.startsWith("/artifacts")
  const isPortalsPage = pathname.startsWith("/portals")

  const shouldExcludeSidebar = excludedPaths.includes(pathname)

  if (shouldExcludeSidebar) {
    return <>{children}</>
  }

  return (
    <div className="pt-12"> {/* Add top padding for fixed system bar */}
      <SidebarProvider>
        {/* System Top Bar - Fixed at top */}
        <SystemTopBar />

        {/* Main Layout with Sidebar */}
        <AppSidebar />
        <SidebarInset className="flex flex-col h-[calc(100vh-3rem)]">
          {/* Sub-Navigation Bar - sticky positioning below system top bar */}
          {!shouldHideSubNavigation && (
            <header className="sticky top-0 z-40 flex h-12 shrink-0 items-center border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4">
              <SubNavigation />
            </header>
          )}

          {/* Sticky Search Bar - positioned below sub-navigation (or at top if no sub-nav) */}
          {shouldShowStickySearch && (
            <LayoutStickySearch />
          )}

          {/* Main Content - different layout for special pages */}
          {isPortalConfigurator ? (
            <div className="flex flex-1 flex-col overflow-hidden">
              {children}
            </div>
          ) : isPoliciesPage || isControlsPage || isWorkflowsPage || isRemediationPage || isAssetsPage || isActivityPage || isOverviewPage || isMonitorPage || isAssessmentsPage ? (
            <div className="flex flex-1 flex-col min-h-0">
              <div className="flex-1 overflow-y-auto p-4">
                {children}
              </div>
            </div>
          ) : isReportsPage || isFrameworksPage || isArtifactsPage || isPortalsPage ? (
            <div className="flex flex-1 flex-col min-h-0">
              <div className="flex-1 overflow-y-auto p-4">
                {children}
              </div>
            </div>
          ) : (
            <div className="flex flex-1 flex-col gap-4 p-4">
              {children}
            </div>
          )}
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}
