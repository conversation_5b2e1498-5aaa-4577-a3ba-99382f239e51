"use client"

import { useState, useMemo } from "react"
import { Plus, Search, Filter, Grid3X3, List } from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StickySearch } from "@/components/sticky-search"
import { PortalCard } from "./portal-card"
import { Portal } from "@/types/portal"
import { cn } from "@/lib/utils"

// Mock data - in a real app, this would come from an API
const mockPortals: Portal[] = [
  {
    id: "1",
    name: "SOC 2 Auditor Portal",
    status: "active",
    stakeholderType: "auditor",
    lastActivity: "2 hours ago",
    activeSessions: 3,
    monthlyViews: 127,
    configurationStatus: "complete",
    description: "Dedicated portal for SOC 2 compliance auditors",
    url: "https://soc2.portal.grcos.com",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20"
  },
  {
    id: "2",
    name: "Customer Trust Center",
    status: "active",
    stakeholderType: "customer",
    lastActivity: "1 day ago",
    activeSessions: 12,
    monthlyViews: 1543,
    configurationStatus: "complete",
    description: "Public-facing customer trust and compliance portal",
    url: "https://trust.grcos.com",
    createdAt: "2024-01-10",
    updatedAt: "2024-01-19"
  },
  {
    id: "3",
    name: "Vendor Assessment Portal",
    status: "draft",
    stakeholderType: "vendor",
    lastActivity: "3 days ago",
    activeSessions: 0,
    monthlyViews: 45,
    configurationStatus: "needs-setup",
    description: "Portal for vendor security assessments",
    createdAt: "2024-01-18",
    updatedAt: "2024-01-18"
  },
  {
    id: "4",
    name: "Regulatory Compliance Hub",
    status: "suspended",
    stakeholderType: "regulator",
    lastActivity: "1 week ago",
    activeSessions: 0,
    monthlyViews: 89,
    configurationStatus: "warning",
    description: "Portal for regulatory compliance reporting",
    url: "https://regulatory.portal.grcos.com",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-12"
  },
  {
    id: "5",
    name: "ISO 27001 Auditor Access",
    status: "active",
    stakeholderType: "auditor",
    lastActivity: "5 hours ago",
    activeSessions: 2,
    monthlyViews: 234,
    configurationStatus: "complete",
    description: "Specialized portal for ISO 27001 auditors",
    url: "https://iso27001.portal.grcos.com",
    createdAt: "2024-01-08",
    updatedAt: "2024-01-20"
  },
  {
    id: "6",
    name: "Partner Integration Portal",
    status: "active",
    stakeholderType: "vendor",
    lastActivity: "6 hours ago",
    activeSessions: 8,
    monthlyViews: 567,
    configurationStatus: "complete",
    description: "Portal for strategic partner integrations",
    url: "https://partners.portal.grcos.com",
    createdAt: "2024-01-12",
    updatedAt: "2024-01-20"
  }
]

type ViewMode = 'grid' | 'list'
type FilterType = 'all' | 'active' | 'draft' | 'suspended' | 'expired'
type StakeholderFilter = 'all' | 'auditor' | 'customer' | 'vendor' | 'regulator'

export function PortalDashboard() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [currentSort, setCurrentSort] = useState("")
  const [statusFilter, setStatusFilter] = useState<FilterType>("all")
  const [stakeholderFilter, setStakeholderFilter] = useState<StakeholderFilter>("all")
  const [viewMode, setViewMode] = useState<ViewMode>("grid")
  const [portals, setPortals] = useState<Portal[]>(mockPortals)

  // Filter and sort options for StickySearch
  const filterOptions = [
    { label: "Active", value: "active" },
    { label: "Draft", value: "draft" },
    { label: "Suspended", value: "suspended" },
    { label: "Expired", value: "expired" },
    { label: "Auditor", value: "auditor" },
    { label: "Customer", value: "customer" },
    { label: "Vendor", value: "vendor" },
    { label: "Regulator", value: "regulator" },
    { label: "Complete", value: "complete" },
    { label: "Needs Setup", value: "needs-setup" },
    { label: "Warning", value: "warning" },
  ]

  const sortOptions = [
    { label: "Name (A-Z)", value: "name-asc" },
    { label: "Name (Z-A)", value: "name-desc" },
    { label: "Created (Newest)", value: "created-desc" },
    { label: "Created (Oldest)", value: "created-asc" },
    { label: "Last Activity", value: "activity" },
    { label: "Monthly Views", value: "views" },
    { label: "Status", value: "status" },
  ]

  const filteredPortals = useMemo(() => {
    return portals.filter(portal => {
      const matchesSearch = portal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           portal.description?.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === "all" || portal.status === statusFilter
      const matchesStakeholder = stakeholderFilter === "all" || portal.stakeholderType === stakeholderFilter
      
      return matchesSearch && matchesStatus && matchesStakeholder
    })
  }, [portals, searchQuery, statusFilter, stakeholderFilter])

  const handleStatusToggle = (portalId: string, newStatus: 'active' | 'suspended') => {
    setPortals(prev => prev.map(portal => 
      portal.id === portalId ? { ...portal, status: newStatus } : portal
    ))
  }

  const handleDuplicate = (portalId: string) => {
    const portalToDuplicate = portals.find(p => p.id === portalId)
    if (portalToDuplicate) {
      const newPortal: Portal = {
        ...portalToDuplicate,
        id: Date.now().toString(),
        name: `${portalToDuplicate.name} (Copy)`,
        status: 'draft',
        activeSessions: 0,
        monthlyViews: 0,
        url: undefined,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0]
      }
      setPortals(prev => [newPortal, ...prev])
    }
  }

  const handleDelete = (portalId: string) => {
    setPortals(prev => prev.filter(portal => portal.id !== portalId))
  }

  const getStatusCounts = () => {
    return {
      all: portals.length,
      active: portals.filter(p => p.status === 'active').length,
      draft: portals.filter(p => p.status === 'draft').length,
      suspended: portals.filter(p => p.status === 'suspended').length,
      expired: portals.filter(p => p.status === 'expired').length,
    }
  }

  const statusCounts = getStatusCounts()

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Search */}
      <StickySearch
        placeholder="Search portals by name or description..."
        onSearch={setSearchQuery}
        onFilterChange={setActiveFilters}
        onSortChange={setCurrentSort}
        filterOptions={filterOptions}
        sortOptions={sortOptions}
        actionButton={
          <Button asChild>
            <Link href="/portals/configure/new">
              <Plus className="h-4 w-4 mr-2" />
              Create Portal
            </Link>
          </Button>
        }
      />

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* View Mode Toggle */}
        <div className="flex justify-end">
          <div className="flex items-center gap-1 border rounded-md p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8 w-8 p-0"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8 w-8 p-0"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

      {/* Portal Grid */}
      <div className={cn(
        "grid gap-6",
        viewMode === 'grid' ? "md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"
      )}>
        {filteredPortals.map((portal) => (
          <PortalCard
            key={portal.id}
            portal={portal}
            onStatusToggle={handleStatusToggle}
            onDuplicate={handleDuplicate}
            onDelete={handleDelete}
          />
        ))}
      </div>

        {filteredPortals.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No portals found matching your criteria.</p>
            <Button asChild className="mt-4">
              <Link href="/portals/configure/new">
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Portal
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
