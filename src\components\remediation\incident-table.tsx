"use client"

import React from "react"
import Link from "next/link"
import { 
  MoreHorizontal, 
  AlertTriangle, 
  Clock, 
  Users, 
  Activity,
  Search,
  Filter,
  Download,
  Play,
  Pause,
  CheckCircle
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { Incident, IncidentSeverity, IncidentStatus } from "@/types/remediation"
import { cn } from "@/lib/utils"

interface IncidentTableProps {
  incidents: Incident[]
}

const severityConfig = {
  low: { label: "Low", variant: "outline" as const, color: "text-green-600" },
  medium: { label: "Medium", variant: "secondary" as const, color: "text-yellow-600" },
  high: { label: "High", variant: "default" as const, color: "text-orange-600" },
  critical: { label: "Critical", variant: "destructive" as const, color: "text-red-600" },
}

const statusConfig = {
  new: { label: "New", variant: "secondary" as const, icon: AlertTriangle },
  acknowledged: { label: "Acknowledged", variant: "default" as const, icon: Clock },
  investigating: { label: "Investigating", variant: "default" as const, icon: Activity },
  contained: { label: "Contained", variant: "outline" as const, icon: CheckCircle },
  resolved: { label: "Resolved", variant: "outline" as const, icon: CheckCircle },
  closed: { label: "Closed", variant: "outline" as const, icon: CheckCircle },
}

const categoryConfig = {
  security: { label: "Security", color: "bg-red-100 text-red-800" },
  availability: { label: "Availability", color: "bg-blue-100 text-blue-800" },
  performance: { label: "Performance", color: "bg-yellow-100 text-yellow-800" },
  "data-breach": { label: "Data Breach", color: "bg-red-100 text-red-800" },
  malware: { label: "Malware", color: "bg-purple-100 text-purple-800" },
  phishing: { label: "Phishing", color: "bg-orange-100 text-orange-800" },
  ddos: { label: "DDoS", color: "bg-indigo-100 text-indigo-800" },
  "insider-threat": { label: "Insider Threat", color: "bg-red-100 text-red-800" },
  compliance: { label: "Compliance", color: "bg-green-100 text-green-800" },
  other: { label: "Other", color: "bg-gray-100 text-gray-800" },
}

export function IncidentTable({ incidents }: IncidentTableProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [severityFilter, setSeverityFilter] = React.useState<string>("all")
  const [statusFilter, setStatusFilter] = React.useState<string>("all")
  const [categoryFilter, setCategoryFilter] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("detectedAt")

  const filteredIncidents = React.useMemo(() => {
    return incidents.filter((incident) => {
      const matchesSearch = incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           incident.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           incident.id.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesSeverity = severityFilter === "all" || incident.severity === severityFilter
      const matchesStatus = statusFilter === "all" || incident.status === statusFilter
      const matchesCategory = categoryFilter === "all" || incident.category === categoryFilter

      return matchesSearch && matchesSeverity && matchesStatus && matchesCategory
    }).sort((a, b) => {
      switch (sortBy) {
        case "title":
          return a.title.localeCompare(b.title)
        case "severity":
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
          return severityOrder[b.severity] - severityOrder[a.severity]
        case "status":
          return a.status.localeCompare(b.status)
        case "detectedAt":
        default:
          return new Date(b.detectedAt).getTime() - new Date(a.detectedAt).getTime()
      }
    })
  }, [incidents, searchTerm, severityFilter, statusFilter, categoryFilter, sortBy])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString()
  }

  const getTimeSinceDetection = (detectedAt: string) => {
    const now = new Date()
    const detected = new Date(detectedAt)
    const diffMs = now.getTime() - detected.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m ago`
    }
    return `${diffMinutes}m ago`
  }

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search incidents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-64"
            />
          </div>
          <Select value={severityFilter} onValueChange={setSeverityFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Severity</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="acknowledged">Acknowledged</SelectItem>
              <SelectItem value="investigating">Investigating</SelectItem>
              <SelectItem value="contained">Contained</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
            </SelectContent>
          </Select>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="security">Security</SelectItem>
              <SelectItem value="data-breach">Data Breach</SelectItem>
              <SelectItem value="malware">Malware</SelectItem>
              <SelectItem value="phishing">Phishing</SelectItem>
              <SelectItem value="ddos">DDoS</SelectItem>
              <SelectItem value="insider-threat">Insider Threat</SelectItem>
              <SelectItem value="compliance">Compliance</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="detectedAt">Detection Time</SelectItem>
              <SelectItem value="severity">Severity</SelectItem>
              <SelectItem value="status">Status</SelectItem>
              <SelectItem value="title">Title</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            More Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredIncidents.length} of {incidents.length} incidents
      </div>

      {/* Incidents Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Incident</TableHead>
              <TableHead>Severity</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Assigned Team</TableHead>
              <TableHead>Detected</TableHead>
              <TableHead>Impact</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredIncidents.map((incident) => {
              const severityInfo = severityConfig[incident.severity]
              const statusInfo = statusConfig[incident.status]
              const categoryInfo = categoryConfig[incident.category]
              const StatusIcon = statusInfo.icon
              
              return (
                <TableRow key={incident.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <Link
                        href={`/remediation/incidents/${incident.id}`}
                        className="font-medium hover:text-primary cursor-pointer"
                      >
                        {incident.title}
                      </Link>
                      <p className="text-xs text-muted-foreground">
                        {incident.id}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {getTimeSinceDetection(incident.detectedAt)}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={severityInfo.variant} className={severityInfo.color}>
                      {severityInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={statusInfo.variant}>
                      <StatusIcon className="mr-1 h-3 w-3" />
                      {statusInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={categoryInfo.color}>
                      {categoryInfo.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="text-sm">{incident.assignedTeam}</p>
                      {incident.assignedTo && (
                        <p className="text-xs text-muted-foreground">{incident.assignedTo}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="text-sm">{formatDate(incident.detectedAt)}</p>
                      <p className="text-xs text-muted-foreground">{formatTime(incident.detectedAt)}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="text-sm">Business: {incident.businessImpact}</p>
                      <p className="text-xs text-muted-foreground">
                        Technical: {incident.technicalImpact}
                      </p>
                      {incident.affectedUsers && (
                        <p className="text-xs text-muted-foreground">
                          {incident.affectedUsers} users affected
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Activity className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Users className="mr-2 h-4 w-4" />
                          Assign Team
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Play className="mr-2 h-4 w-4" />
                          Run Playbook
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Mark Resolved
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
}
