"use client"

import * as React from "react"
import { Search, Filter, SortAsc, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface StickySearchProps {
  placeholder?: string
  onSearch?: (query: string) => void
  onFilterChange?: (filters: string[]) => void
  onSortChange?: (sort: string) => void
  filterOptions?: Array<{ label: string; value: string }>
  sortOptions?: Array<{ label: string; value: string }>
  className?: string
  showFilters?: boolean
  showSort?: boolean
  actionButton?: React.ReactNode
}

export function StickySearch({
  placeholder = "Search...",
  onSearch,
  onFilter<PERSON>hange,
  onSortChange,
  filterOptions = [],
  sortOptions = [],
  className,
  showFilters = true,
  showSort = true,
  actionButton,
}: StickySearchProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [activeFilters, setActiveFilters] = React.useState<string[]>([])
  const [currentSort, setCurrentSort] = React.useState("")

  // Handle search input changes
  const handleSearchChange = React.useCallback((value: string) => {
    setSearchQuery(value)
    onSearch?.(value)
  }, [onSearch])

  // Handle filter changes
  const handleFilterToggle = React.useCallback((filterValue: string) => {
    const newFilters = activeFilters.includes(filterValue)
      ? activeFilters.filter(f => f !== filterValue)
      : [...activeFilters, filterValue]
    
    setActiveFilters(newFilters)
    onFilterChange?.(newFilters)
  }, [activeFilters, onFilterChange])

  // Handle sort changes
  const handleSortChange = React.useCallback((sortValue: string) => {
    setCurrentSort(sortValue)
    onSortChange?.(sortValue)
  }, [onSortChange])

  // Clear all filters
  const clearFilters = React.useCallback(() => {
    setActiveFilters([])
    onFilterChange?.([])
  }, [onFilterChange])

  // Clear search
  const clearSearch = React.useCallback(() => {
    setSearchQuery("")
    onSearch?.("")
  }, [onSearch])

  return (
    <div className={cn(
      "sticky top-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-3",
      className
    )}>
      <div className="flex items-center gap-3">
        {/* Search Input */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder={placeholder}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-9 pr-9 h-9"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-1 top-1/2 h-7 w-7 p-0 -translate-y-1/2 hover:bg-muted"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filter Dropdown */}
        {showFilters && filterOptions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFilters.length > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    {activeFilters.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {filterOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleFilterToggle(option.value)}
                  className="flex items-center justify-between"
                >
                  <span>{option.label}</span>
                  {activeFilters.includes(option.value) && (
                    <div className="h-2 w-2 rounded-full bg-primary" />
                  )}
                </DropdownMenuItem>
              ))}
              {activeFilters.length > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={clearFilters} className="text-muted-foreground">
                    Clear all filters
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Sort Dropdown */}
        {showSort && sortOptions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <SortAsc className="h-4 w-4 mr-2" />
                Sort
                {currentSort && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {sortOptions.find(s => s.value === currentSort)?.label}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuLabel>Sort Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {sortOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => handleSortChange(option.value)}
                  className="flex items-center justify-between"
                >
                  <span>{option.label}</span>
                  {currentSort === option.value && (
                    <div className="h-2 w-2 rounded-full bg-primary" />
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Active Filters Display */}
        {activeFilters.length > 0 && (
          <div className="flex items-center gap-2 flex-wrap">
            {activeFilters.map((filter) => {
              const filterOption = filterOptions.find(f => f.value === filter)
              return (
                <Badge
                  key={filter}
                  variant="secondary"
                  className="text-xs flex items-center gap-1"
                >
                  {filterOption?.label || filter}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFilterToggle(filter)}
                    className="h-3 w-3 p-0 hover:bg-muted-foreground/20"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )
            })}
          </div>
        )}

        {/* Action Button */}
        {actionButton && (
          <div className="flex-shrink-0">
            {actionButton}
          </div>
        )}
      </div>
    </div>
  )
}
