"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { ThemeToggle } from "@/components/theme-toggle"
import { Separator } from "@/components/ui/separator"
import { navigationData } from "@/lib/navigation-data"
import { AIChatbot } from "@/components/ai-chatbot"

export function SystemTopBar() {
  const pathname = usePathname()
  const [currentTime, setCurrentTime] = React.useState(new Date())

  // Update time every second
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Find the current active main item
  const currentModule = React.useMemo(() => {
    for (const mainItem of navigationData.navMain) {
      if (pathname === mainItem.url || pathname.startsWith(mainItem.url + "/")) {
        return mainItem.title
      }
    }
    return "GRCOS"
  }, [pathname])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-12 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 flex items-center px-4">
      {/* Left side - Sidebar toggle and current module */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1.5">
          <SidebarTrigger className="h-8 w-8" />
          <span className="text-xs text-muted-foreground font-mono">Alt+S</span>
        </div>
        <Separator orientation="vertical" className="h-6" />
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {currentModule}
          </span>
        </div>
      </div>

      {/* Center spacer */}
      <div className="flex-1" />

      {/* Right side - Date/time, theme toggle, and AI chatbot */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">
            {formatDate(currentTime)}
          </span>
          <Separator orientation="vertical" className="h-4" />
          <span className="font-mono">
            {formatTime(currentTime)}
          </span>
        </div>
        <Separator orientation="vertical" className="h-6" />
        <ThemeToggle />
        <Separator orientation="vertical" className="h-6" />
        <AIChatbot />
      </div>
    </div>
  )
}
