import {
  Activity,
  Archive,
  BarChart3,
  FileText,
  LayoutDashboard,
  Monitor,
  Package,
  Shield,
  ShieldCheck,
  Users,
  Workflow,
  Wrench,
  type LucideIcon,
} from "lucide-react"

export interface SubModule {
  title: string
  url: string
  description?: string
}

export interface NavigationItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  subModules?: SubModule[]
}

export interface NavigationData {
  user: {
    name: string
    email: string
    avatar: string
  }
  navMain: NavigationItem[]
}

// GRCOS navigation data - Flat, Agent-Orchestrated Architecture
export const navigationData: NavigationData = {
  user: {
    name: "GRC Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  // Entity management is now handled by EntitySwitcher component
  // Teams section replaced with multi-entity functionality
  navMain: [
    {
      title: "Overview",
      url: "/overview",
      icon: LayoutDashboard,
      subModules: [
        { title: "Executive Summary", url: "/overview/executive", description: "High-level executive dashboard view" },
        { title: "System Agent Status", url: "/overview/agents", description: "Real-time agent activity and orchestration health" },
        { title: "Risk Posture", url: "/overview/risk", description: "Cross-domain risk posture overview" },
        { title: "Compliance Status", url: "/overview/compliance", description: "Overall compliance status across frameworks" },
      ]
    },
    {
      title: "Activity",
      url: "/activity",
      icon: Activity,
      subModules: [
        { title: "Tasks", url: "/activity/tasks", description: "Personal task management dashboard with workload analytics" },
        { title: "Notifications", url: "/activity/notifications", description: "Role-based notification center with intelligent filtering" },
        { title: "Approvals", url: "/activity/approvals", description: "Approval & review workflows interface with delegation capabilities" },
        { title: "Communications", url: "/activity/communications", description: "Communication hub for team collaboration and knowledge sharing" },
        { title: "Analytics", url: "/activity/analytics", description: "Performance analytics dashboard with productivity metrics" },
        { title: "Knowledge", url: "/activity/knowledge", description: "Knowledge context panel with contextual information" },
        { title: "Navigation Test", url: "/activity/navigation-test", description: "Sub-navigation testing and verification interface" },
      ]
    },
    {
      title: "Assets",
      url: "/assets",
      icon: Package,
      subModules: [
        { title: "Overview", url: "/assets", description: "Asset portfolio overview with comprehensive dashboard and statistics" },
        { title: "IT Assets", url: "/assets/it", description: "Servers, workstations, network infrastructure with hardware specifications and software inventory" },
        { title: "OT Assets", url: "/assets/ot", description: "Industrial control systems, SCADA, manufacturing equipment with operational technology details" },
        { title: "IoT Devices", url: "/assets/iot", description: "Connected sensors, smart devices, edge computing with connectivity and security status" },
        { title: "Identities", url: "/assets/identities", description: "Users, service accounts, privileged access management with access rights and permissions" },
        { title: "Applications", url: "/assets/applications", description: "Software inventory, cloud services, SaaS platforms with security configurations and dependencies" },
        { title: "Vendors", url: "/assets/vendors", description: "Third-party relationships, supplier risk management with contract details and risk assessments" },
        { title: "Processes", url: "/assets/processes", description: "Business processes, workflows, procedures with asset dependencies and compliance requirements" },
        { title: "Search", url: "/assets/search", description: "Global asset search with advanced filtering and discovery across all categories" },
        { title: "Topology", url: "/assets/topology", description: "Asset relationships, dependencies, and network topology visualization" },
        { title: "Discovery", url: "/assets/discovery", description: "Asset discovery configuration and monitoring with automated scanning" },
        { title: "Register", url: "/assets/register", description: "Manual asset registration with comprehensive form and bulk import capabilities" },
        { title: "Navigation Test", url: "/assets/navigation-test", description: "Sub-navigation testing and verification interface" },
      ]
    },
    {
      title: "Monitor",
      url: "/monitor",
      icon: Monitor,
      subModules: [
        { title: "Security Dashboard", url: "/monitor/security", description: "Real-time security monitoring across all asset categories" },
        { title: "Anomaly Detection", url: "/monitor/anomaly", description: "AI-powered anomaly detection and behavioral analysis" },
        { title: "SIEM Dashboard", url: "/monitor/siem", description: "Unified SIEM dashboard with Wazuh integration" },
        { title: "Threat Correlation", url: "/monitor/correlation", description: "Cross-environment threat correlation (IT/OT/IoT)" },
        { title: "Alert Prioritization", url: "/monitor/alerts", description: "System Agent coordinated alert prioritization" },
      ]
    },
    {
      title: "Frameworks",
      url: "/frameworks",
      icon: Shield,
      // No sub-modules - single unified frameworks interface
    },
    {
      title: "Controls",
      url: "/controls",
      icon: ShieldCheck,
      items: [
        { title: "Overview", url: "/controls/overview", description: "Controls management dashboard and effectiveness metrics" },
        { title: "All Controls", url: "/controls/all", description: "Complete controls repository and management interface" },
        { title: "Testing", url: "/controls/testing", description: "Control testing schedules, results, and evidence" },
        { title: "Effectiveness", url: "/controls/effectiveness", description: "Control effectiveness monitoring and reporting" },
      ]
    },
    {
      title: "Policies",
      url: "/policies",
      icon: FileText,
      items: [
        { title: "Overview", url: "/policies/overview", description: "Policy management dashboard and analytics" },
        { title: "All Policies", url: "/policies/all", description: "Complete policy repository and management interface" },
        { title: "Violations", url: "/policies/violations", description: "Policy violation tracking and remediation" },
        { title: "Exceptions", url: "/policies/exceptions", description: "Policy exception requests and approvals" },
      ]
    },
    {
      title: "Assessments",
      url: "/assessments",
      icon: BarChart3,
      subModules: [
        { title: "Risk Assessments", url: "/assessments/risk", description: "Automated risk assessments and gap analysis" },
        { title: "Control Testing", url: "/assessments/testing", description: "Assessment Agent orchestrated control testing" },
        { title: "Gap Analysis", url: "/assessments/gaps", description: "Compliance gap identification and analysis" },
        { title: "Quantitative Risk", url: "/assessments/quantitative", description: "Quantitative risk analysis with Open Source Risk Engine" },
        { title: "Continuous Assessment", url: "/assessments/continuous", description: "Continuous assessment scheduling and execution" },
      ]
    },
    {
      title: "Workflows",
      url: "/workflows",
      icon: Workflow,
      subModules: [
        { title: "Operations", url: "/workflows/operations", description: "Active workflows, pending approvals, failed executions, and scheduled workflows" },
        { title: "Library", url: "/workflows/library", description: "Process templates, custom workflows, integration workflows, and workflow metrics" },
        { title: "Designer", url: "/workflows/designer", description: "Visual workflow designer, template catalog, integration connectors, and testing environment" },
      ]
    },
    {
      title: "Remediation",
      url: "/remediation",
      icon: Wrench,
      subModules: [
        { title: "Incidents", url: "/remediation/incidents", description: "Incident Command Center with active incidents, response timeline, resource status, and escalation queue" },
        { title: "Investigation", url: "/remediation/investigation", description: "DFIR-IRIS cases, evidence repository, timeline reconstruction, and IOC tracking" },
        { title: "Automation", url: "/remediation/automation", description: "Playbook execution, containment actions, recovery operations, and integration health" },
        { title: "Analytics", url: "/remediation/analytics", description: "Response metrics, team performance, incident patterns, and compliance dashboard" },
      ]
    },
    {
      title: "Reports",
      url: "/reports",
      icon: FileText,
      // No sub-modules - single unified reports interface
    },
    {
      title: "Artifacts",
      url: "/artifacts",
      icon: Archive,
      // No sub-modules - single unified artifacts interface
    },
    {
      title: "Portals",
      url: "/portals",
      icon: Users,
      // No sub-modules - portal dashboard is the primary interface
    },
  ],
}
